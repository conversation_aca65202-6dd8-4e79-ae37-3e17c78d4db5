import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MemberService } from '../../../services/member.service';
import { ToastrService } from 'ngx-toastr';
import { Router } from '@angular/router';

@Component({
    selector: 'app-member-add',
    templateUrl: './member-add.component.html',
    styleUrls: ['./member-add.component.css'],
    standalone: false
})
export class MemberAddComponent implements OnInit, OnDestroy {
  memberAddForm: FormGroup;
  isProcessCompleted: boolean = false;
  isSubmitting: boolean = false;

  // Registration type management
  registrationType: 'qr' | 'card' = 'qr'; // Default QR

  // Required fields for form progress calculation
  requiredFields = ['name', 'phoneNumber', 'gender', 'email'];
  totalFields = 6; // Total number of fields in the form

  constructor(
    private formBuilder: FormBuilder,
    private memberService: MemberService,
    private toastrService: ToastrService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadRegistrationType();
    this.createMemberAddForm();
    this.setupRFIDListener();
  }

  createMemberAddForm() {
    const formConfig: any = {
      name: ['', Validators.required],
      adress: [''],
      gender: ['', Validators.required],
      phoneNumber: ['', Validators.required],
      email: ['', [Validators.required, Validators.email]],
      birthDate: [''],
    };

    // Add card number field for card registration
    if (this.registrationType === 'card') {
      formConfig.cardNumber = ['', Validators.required];
      this.requiredFields = ['name', 'phoneNumber', 'gender', 'email', 'cardNumber'];
    } else {
      this.requiredFields = ['name', 'phoneNumber', 'gender', 'email'];
    }

    this.memberAddForm = this.formBuilder.group(formConfig);
  }

  add() {
    if (this.memberAddForm.invalid) {
      this.toastrService.error('Lütfen işaretli alanları doldurunuz', 'Eksik Bilgi');
      this.isSubmitting = false;

      // Tüm form kontrollerini dokunulmuş olarak işaretle
      Object.keys(this.memberAddForm.controls).forEach(key => {
        const control = this.memberAddForm.get(key);
        if (control) {
          control.markAsTouched();
          control.markAsDirty();
        }
      });

      // Eksik alanları vurgula ve titret
      this.highlightMissingFields();

      return;
    }

    this.isSubmitting = true;
    let memberModel = Object.assign({}, this.memberAddForm.value);

    if (memberModel.name) {
      memberModel.name = memberModel.name.toUpperCase();
    }

    if (memberModel.birthDate === '') {
      memberModel.birthDate = null;
    }
    if (memberModel.adress === '') {
      memberModel.adress = null;
    }

    // Kartlı kayıt için cardNumber'ı scanNumber'a çevir
    if (this.registrationType === 'card' && memberModel.cardNumber) {
      memberModel.scanNumber = memberModel.cardNumber;
      delete memberModel.cardNumber; // cardNumber'ı backend'e gönderme
    }

    // E-posta artık zorunlu olduğu için bu kontrole gerek yok
    // if (memberModel.email === '') {
    //   memberModel.email = null;
    // }

    // Choose API based on registration type
    const apiCall = this.registrationType === 'card'
      ? this.memberService.addWithCard(memberModel)
      : this.memberService.add(memberModel);

    apiCall.subscribe(
      (response) => {
        const successMessage = this.registrationType === 'card'
          ? 'Üye kartlı sisteme başarıyla eklendi'
          : 'Üye QR kodlu sisteme başarıyla eklendi';
        this.toastrService.success(successMessage, 'Başarılı');
        this.isProcessCompleted = true;
        this.isSubmitting = false;
        setTimeout(() => {
          this.resetForm();
        }, 2000);
      },
      (responseError) => {
        if (responseError.error && responseError.error.Errors) {
          for (let i = 0; i < responseError.error.Errors.length; i++) {
            this.toastrService.error(
              responseError.error.Errors[i].ErrorMessage,
              'Hata'
            );
          }
        } else {
          this.toastrService.error('Beklenmedik bir hata oluştu', 'Hata');
        }
        this.isSubmitting = false;
      }
    );
  }

  resetForm() {
    const resetData: any = {
      name: '',
      adress: '',
      gender: '',
      phoneNumber: '',
      email: '',
      birthDate: '',
    };

    if (this.registrationType === 'card') {
      resetData.cardNumber = '';
    }

    this.memberAddForm.reset(resetData);
  }

  // Registration type management methods
  loadRegistrationType() {
    const savedType = localStorage.getItem('memberRegistrationType');
    if (savedType === 'card' || savedType === 'qr') {
      this.registrationType = savedType;
    }
  }

  saveRegistrationType() {
    localStorage.setItem('memberRegistrationType', this.registrationType);
  }

  switchToQRRegistration() {
    this.registrationType = 'qr';
    this.saveRegistrationType();
    this.createMemberAddForm();
    this.toastrService.info('QR Kodlu kayıt moduna geçildi', 'Kayıt Modu');
  }

  switchToCardRegistration() {
    this.registrationType = 'card';
    this.saveRegistrationType();
    this.createMemberAddForm();
    this.toastrService.info('Kartlı kayıt moduna geçildi', 'Kayıt Modu');
  }

  // Card input management
  enableCardInput() {
    const cardInput = document.getElementById('cardNumber') as HTMLInputElement;
    if (cardInput) {
      cardInput.removeAttribute('readonly');
      cardInput.focus();
      cardInput.placeholder = 'Kart numarasını manuel olarak girin...';
      this.toastrService.info('Manuel giriş aktif edildi', 'Kart Girişi');
    }
  }

  // RFID Card Reader Integration
  private rfidBuffer: string = '';
  private rfidTimeout: any;
  private rfidKeyListener: (event: KeyboardEvent) => void;

  setupRFIDListener() {
    // Create the listener function
    this.rfidKeyListener = (event: KeyboardEvent) => {
      // Only process if we're in card registration mode and card input is focused or readonly
      if (this.registrationType === 'card') {
        const cardInput = document.getElementById('cardNumber') as HTMLInputElement;

        if (cardInput && (document.activeElement === cardInput || cardInput.hasAttribute('readonly'))) {
          // RFID readers typically send data very quickly and end with Enter
          if (event.key === 'Enter') {
            if (this.rfidBuffer.length > 0) {
              // Set the card number and clear buffer
              this.memberAddForm.patchValue({ cardNumber: this.rfidBuffer.trim() });
              this.toastrService.success(`Kart okundu: ${this.rfidBuffer.trim()}`, 'RFID Kart');
              this.rfidBuffer = '';

              // Clear any existing timeout
              if (this.rfidTimeout) {
                clearTimeout(this.rfidTimeout);
              }
            }
          } else {
            // Add character to buffer
            this.rfidBuffer += event.key;

            // Clear buffer after 100ms of inactivity (in case Enter is missed)
            if (this.rfidTimeout) {
              clearTimeout(this.rfidTimeout);
            }
            this.rfidTimeout = setTimeout(() => {
              if (this.rfidBuffer.length > 5) { // Minimum reasonable card number length
                this.memberAddForm.patchValue({ cardNumber: this.rfidBuffer.trim() });
                this.toastrService.success(`Kart okundu: ${this.rfidBuffer.trim()}`, 'RFID Kart');
              }
              this.rfidBuffer = '';
            }, 100);
          }

          // Prevent default behavior to avoid typing in other fields
          if (cardInput.hasAttribute('readonly')) {
            event.preventDefault();
          }
        }
      }
    };

    // Add the event listener
    document.addEventListener('keypress', this.rfidKeyListener);
  }

  ngOnDestroy(): void {
    // Clean up event listener
    if (this.rfidKeyListener) {
      document.removeEventListener('keypress', this.rfidKeyListener);
    }

    // Clear any pending timeout
    if (this.rfidTimeout) {
      clearTimeout(this.rfidTimeout);
    }
  }

  // Eksik alanları vurgulama metodu
  highlightMissingFields() {
    // Zorunlu alanları kontrol et ve eksik olanları belirle
    const missingFields: string[] = [];
    const requiredControls: (HTMLElement | null)[] = [];

    // Ad Soyad kontrolü
    const nameControl = this.memberAddForm.get('name');
    if (nameControl?.invalid) {
      missingFields.push('Ad Soyad');
      requiredControls.push(document.getElementById('name'));
    }

    // Telefon Numarası kontrolü
    const phoneControl = this.memberAddForm.get('phoneNumber');
    if (phoneControl?.invalid) {
      missingFields.push('Telefon Numarası');
      requiredControls.push(document.getElementById('phoneNumber'));
    }

    // Cinsiyet kontrolü
    const genderControl = this.memberAddForm.get('gender');
    if (genderControl?.invalid) {
      missingFields.push('Cinsiyet');
      requiredControls.push(document.getElementById('gender'));
    }

    // E-posta kontrolü
    const emailControl = this.memberAddForm.get('email');
    if (emailControl?.invalid) {
      missingFields.push('E-posta');
      requiredControls.push(document.getElementById('email'));
    }

    // Kart numarası kontrolü (sadece kartlı kayıt için)
    if (this.registrationType === 'card') {
      const cardControl = this.memberAddForm.get('cardNumber');
      if (cardControl?.invalid) {
        missingFields.push('Kart Numarası');
        requiredControls.push(document.getElementById('cardNumber'));
      }
    }

    // Eksik alan sayısına göre mesaj göster
    if (missingFields.length > 0) {
      const fieldList = missingFields.join(', ');
      this.toastrService.warning(`Lütfen şu alanları doldurun: ${fieldList}`, 'Eksik Alanlar');

      // Eksik alanları görsel olarak vurgula ve titret
      setTimeout(() => {
        requiredControls.forEach((element, index) => {
          if (element) {
            // Titreşim animasyonu ekle
            element.classList.add('shake-animation');

            // İlk eksik alana kaydır
            if (index === 0) {
              element.scrollIntoView({ behavior: 'smooth', block: 'center' });
              element.focus(); // İlk alana odaklan
            }

            // Animasyonu bir süre sonra kaldır
            setTimeout(() => {
              element.classList.remove('shake-animation');
            }, 600);
          }
        });
      }, 100);
    }
  }

  /**
   * Calculate the form completion progress as a percentage
   * @returns number between 0-100
   */
  getFormProgress(): number {
    if (!this.memberAddForm) return 0;

    // Count filled required fields
    let filledRequiredFields = 0;
    for (const field of this.requiredFields) {
      if (this.memberAddForm.get(field)?.valid) {
        filledRequiredFields++;
      }
    }

    // Count filled optional fields
    let filledOptionalFields = 0;
    const optionalFields = Object.keys(this.memberAddForm.controls)
      .filter(key => !this.requiredFields.includes(key));

    for (const field of optionalFields) {
      if (this.memberAddForm.get(field)?.value) {
        filledOptionalFields++;
      }
    }

    // Calculate progress - required fields have more weight
    const requiredWeight = 0.7; // 70% of progress is from required fields
    const optionalWeight = 0.3; // 30% of progress is from optional fields

    const requiredProgress = (filledRequiredFields / this.requiredFields.length) * requiredWeight * 100;
    const optionalProgress = (filledOptionalFields / optionalFields.length) * optionalWeight * 100;

    return Math.round(requiredProgress + optionalProgress);
  }
}
