<div class="container-fluid mt-4 fade-in">
  <!-- Loading Spinner -->
  <app-loading-spinner
    *ngIf="isSubmitting"
    [overlay]="true"
    [sidebarAware]="true"
    [showText]="true"
    text="Üye ekleniyor">
  </app-loading-spinner>

  <!-- Page Header with Help Button -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="d-flex align-items-center gap-2">
            <h5 class="mb-0">
              <i class="fas fa-user-plus me-2"></i>
              Yeni <PERSON><PERSON>
            </h5>

            <!-- Help Button -->
            <app-help-button
              guideId="member-add"
              position="inline"
              size="small"
              tooltip="Bu panel hakkında yardım al">
            </app-help-button>
          </div>
          <p class="text-muted mb-0 mt-1">
            Yeni üye kaydı oluşturun ve üyelik bilgilerini girin
          </p>
        </div>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col-md-12" [class.content-blur]="isSubmitting">
      <div class="modern-card">
        <div class="modern-card-header">
          <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0">
              <i class="fas fa-user-plus me-2"></i>
              Üye Bilgileri Formu
            </h6>

            <!-- Registration Type Toggle -->
            <div class="btn-group" role="group">
              <button
                type="button"
                class="btn btn-sm"
                [class.btn-primary]="registrationType === 'qr'"
                [class.btn-outline-primary]="registrationType !== 'qr'"
                (click)="switchToQRRegistration()"
                [disabled]="isSubmitting">
                <i class="fas fa-qrcode me-1"></i>
                QR Kodlu Kayıt
              </button>
              <button
                type="button"
                class="btn btn-sm"
                [class.btn-success]="registrationType === 'card'"
                [class.btn-outline-success]="registrationType !== 'card'"
                (click)="switchToCardRegistration()"
                [disabled]="isSubmitting">
                <i class="fas fa-credit-card me-1"></i>
                Kartlı Kayıt
              </button>
            </div>
          </div>
        </div>

        <div class="modern-card-body">
          <form [formGroup]="memberAddForm" class="fade-in">
            <!-- Form Progress Indicator -->
            <div class="progress mb-4" style="height: 6px;">
              <div
                class="progress-bar bg-primary"
                [style.width]="getFormProgress() + '%'"
                role="progressbar"
                [attr.aria-valuenow]="getFormProgress()"
                aria-valuemin="0"
                aria-valuemax="100">
              </div>
            </div>

            <!-- Personal Information Section -->
            <div class="form-section mb-4">
              <h6 class="section-title">
                <i class="fas fa-user me-2"></i>
                Kişisel Bilgiler
              </h6>

              <div class="row">
                <div class="col-md-4 mb-3">
                  <div class="modern-form-group" [ngClass]="{'has-error': memberAddForm.get('name')?.invalid && memberAddForm.get('name')?.touched}">
                    <label for="name" class="modern-form-label required">Ad Soyad</label>
                    <div class="input-group">
                      <span class="input-group-text" [ngClass]="{'text-danger': memberAddForm.get('name')?.invalid && memberAddForm.get('name')?.touched}">
                        <i class="fas fa-user"></i>
                      </span>
                      <input
                        type="text"
                        id="name"
                        formControlName="name"
                        class="modern-form-control"
                        placeholder="Ad Soyad"
                        [ngClass]="{'is-invalid': memberAddForm.get('name')?.invalid && memberAddForm.get('name')?.touched}"
                      />
                    </div>
                    <small class="error-message" *ngIf="memberAddForm.get('name')?.invalid && memberAddForm.get('name')?.touched">
                      <i class="fas fa-exclamation-circle me-1"></i>
                      Ad Soyad alanı zorunludur
                    </small>
                  </div>
                </div>

                <div class="col-md-4 mb-3">
                  <div class="modern-form-group" [ngClass]="{'has-error': memberAddForm.get('phoneNumber')?.invalid && memberAddForm.get('phoneNumber')?.touched}">
                    <label for="phoneNumber" class="modern-form-label required">Telefon Numarası</label>
                    <div class="input-group">
                      <span class="input-group-text" [ngClass]="{'text-danger': memberAddForm.get('phoneNumber')?.invalid && memberAddForm.get('phoneNumber')?.touched}">
                        <i class="fas fa-phone"></i>
                      </span>
                      <input
                        type="tel"
                        id="phoneNumber"
                        formControlName="phoneNumber"
                        class="modern-form-control"
                        placeholder="Telefon Numarası"
                        maxlength="11"
                        [ngClass]="{'is-invalid': memberAddForm.get('phoneNumber')?.invalid && memberAddForm.get('phoneNumber')?.touched}"
                      />
                    </div>
                    <small class="error-message" *ngIf="memberAddForm.get('phoneNumber')?.invalid && memberAddForm.get('phoneNumber')?.touched">
                      <i class="fas fa-exclamation-circle me-1"></i>
                      Geçerli bir telefon numarası giriniz
                    </small>
                  </div>
                </div>

                <div class="col-md-4 mb-3">
                  <div class="modern-form-group" [ngClass]="{'has-error': memberAddForm.get('gender')?.invalid && memberAddForm.get('gender')?.touched}">
                    <label for="gender" class="modern-form-label required">Cinsiyet</label>
                    <div class="input-group">
                      <span class="input-group-text" [ngClass]="{'text-danger': memberAddForm.get('gender')?.invalid && memberAddForm.get('gender')?.touched}">
                        <i class="fas fa-venus-mars"></i>
                      </span>
                      <select
                        id="gender"
                        formControlName="gender"
                        class="modern-form-control"
                        [ngClass]="{'is-invalid': memberAddForm.get('gender')?.invalid && memberAddForm.get('gender')?.touched}"
                      >
                        <option value="">Seçiniz</option>
                        <option value="1">Erkek</option>
                        <option value="2">Kadın</option>
                      </select>
                    </div>
                    <small class="error-message" *ngIf="memberAddForm.get('gender')?.invalid && memberAddForm.get('gender')?.touched">
                      <i class="fas fa-exclamation-circle me-1"></i>
                      Cinsiyet seçimi zorunludur
                    </small>
                  </div>
                </div>

                <!-- Card Number Field (only for card registration) -->
                <div class="col-md-4 mb-3" *ngIf="registrationType === 'card'">
                  <div class="modern-form-group" [ngClass]="{'has-error': memberAddForm.get('cardNumber')?.invalid && memberAddForm.get('cardNumber')?.touched}">
                    <label for="cardNumber" class="modern-form-label required">Kart Numarası</label>
                    <div class="input-group">
                      <span class="input-group-text" [ngClass]="{'text-danger': memberAddForm.get('cardNumber')?.invalid && memberAddForm.get('cardNumber')?.touched}">
                        <i class="fas fa-credit-card"></i>
                      </span>
                      <input
                        type="text"
                        id="cardNumber"
                        formControlName="cardNumber"
                        class="modern-form-control"
                        placeholder="RFID kartını okutun..."
                        [ngClass]="{'is-invalid': memberAddForm.get('cardNumber')?.invalid && memberAddForm.get('cardNumber')?.touched}"
                        readonly
                      />
                      <button
                        type="button"
                        class="btn btn-outline-secondary"
                        (click)="enableCardInput()"
                        title="Manuel giriş için tıklayın">
                        <i class="fas fa-edit"></i>
                      </button>
                    </div>
                    <small class="text-muted mt-1">
                      <i class="fas fa-info-circle me-1"></i>
                      RFID kartınızı okutun veya manuel giriş için düzenle butonuna tıklayın
                    </small>
                    <small class="error-message" *ngIf="memberAddForm.get('cardNumber')?.invalid && memberAddForm.get('cardNumber')?.touched">
                      <i class="fas fa-exclamation-circle me-1"></i>
                      Kart numarası zorunludur
                    </small>
                  </div>
                </div>
              </div>
            </div>

            <!-- Additional Information Section -->
            <div class="form-section">
              <h6 class="section-title">
                <i class="fas fa-info-circle me-2"></i>
                İletişim Bilgileri
              </h6>

              <div class="row">
                <div class="col-md-4 mb-3">
                  <div class="modern-form-group">
                    <label for="birthDate" class="modern-form-label">Doğum Tarihi</label>
                    <div class="input-group">
                      <span class="input-group-text"><i class="fas fa-calendar"></i></span>
                      <input
                        type="date"
                        id="birthDate"
                        formControlName="birthDate"
                        class="modern-form-control"
                        min="1924-12-31"
                        max="2024-12-31"
                      />
                    </div>
                  </div>
                </div>

                <div class="col-md-4 mb-3">
                  <div class="modern-form-group">
                    <label for="adress" class="modern-form-label">Adres</label>
                    <div class="input-group">
                      <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                      <input
                        type="text"
                        id="adress"
                        formControlName="adress"
                        class="modern-form-control"
                        placeholder="Adres"
                      />
                    </div>
                  </div>
                </div>

                <div class="col-md-4 mb-3">
                  <div class="modern-form-group" [ngClass]="{'has-error': memberAddForm.get('email')?.invalid && memberAddForm.get('email')?.touched}">
                    <label for="email" class="modern-form-label required">E-posta</label>
                    <div class="input-group">
                      <span class="input-group-text" [ngClass]="{'text-danger': memberAddForm.get('email')?.invalid && memberAddForm.get('email')?.touched}">
                        <i class="fas fa-envelope"></i>
                      </span>
                      <input
                        type="email"
                        id="email"
                        formControlName="email"
                        class="modern-form-control"
                        placeholder="E-posta"
                        required
                        [ngClass]="{'is-invalid': memberAddForm.get('email')?.invalid && memberAddForm.get('email')?.touched}"
                      />
                    </div>
                    <small class="error-message" *ngIf="memberAddForm.get('email')?.invalid && memberAddForm.get('email')?.touched">
                      <i class="fas fa-exclamation-circle me-1"></i>
                      Geçerli bir e-posta adresi giriniz
                    </small>
                    
                    <small class="text-warning mt-1">
                      <i class="fas fa-exclamation-triangle me-1"></i>
                      <span *ngIf="registrationType === 'qr'">
                        E-posta adresi, üyenin QR kodunu alabilmesi ve salonunuza giriş yapabilmesi için gereklidir.
                      </span>
                      <span *ngIf="registrationType === 'card'">
                        E-posta adresi, üyenin hesap bilgilerine erişebilmesi için gereklidir.
                      </span>
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </form>
        </div>

        <div class="modern-card-footer">
          <div class="d-flex justify-content-between align-items-center">
            <button
              class="modern-btn modern-btn-primary modern-btn-lg"
              (click)="add()"
              [disabled]="isSubmitting"
            >
              <i class="fas fa-save modern-btn-icon" *ngIf="!isSubmitting"></i>
              <i class="fas fa-spinner fa-spin modern-btn-icon" *ngIf="isSubmitting"></i>
              {{ isSubmitting ?
                (registrationType === 'card' ? 'Kartlı Üye Ekleniyor...' : 'QR Kodlu Üye Ekleniyor...') :
                (registrationType === 'card' ? 'Kartlı Üye Ekle' : 'QR Kodlu Üye Ekle')
              }}
            </button>

            <a
              *ngIf="isProcessCompleted"
              routerLink="/membership/add"
              class="modern-btn modern-btn-success modern-btn-lg"
            >
              <i class="fas fa-id-card modern-btn-icon"></i>
              Üyelik Tanımlama Paneli
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
