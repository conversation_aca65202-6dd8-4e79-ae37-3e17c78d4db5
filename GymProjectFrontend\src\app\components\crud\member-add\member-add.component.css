/* Member Add Component Styles */

/* Content Blur */
.content-blur {
  filter: blur(3px);
  pointer-events: none;
  transition: filter 0.3s ease;
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

/* Card Styles - Using modern-components.css classes */

/* Form Styles */
.form-section {
  margin-bottom: 1.5rem;
  padding: 1.25rem;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius-md);
  transition: all 0.3s ease;
}

.form-section:hover {
  background-color: var(--bg-tertiary);
  box-shadow: var(--shadow-sm);
}

.section-title {
  margin-bottom: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  display: flex;
  align-items: center;
  font-size: 1rem;
}

.modern-form-group {
  margin-bottom: var(--spacing-md);
  position: relative;
}

/* Hata durumunda form grubu */
.modern-form-group.has-error {
  animation: highlight 1s ease-in-out;
}

@keyframes highlight {
  0% { background-color: rgba(220, 53, 69, 0.1); }
  100% { background-color: transparent; }
}

/* Hata mesajı için stil */
.error-message {
  color: #dc3545;
  font-size: 0.85rem;
  margin-top: 0.25rem;
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

.modern-form-label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: 500;
  color: var(--text-primary);
}

/* Zorunlu alan etiketi için stil */
.modern-form-label.required::after {
  content: ' *';
  color: var(--danger);
}

/* Input Group Styles */
.input-group {
  display: flex;
  width: 100%;
}

.input-group-text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--primary-light);
  color: var(--primary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-md) 0 0 var(--border-radius-md);
  border-right: none;
}

.input-group-text.text-danger {
  background-color: var(--danger-light);
  color: var(--danger);
}

.modern-form-control {
  flex: 1;
  padding: 0.5rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 0 var(--border-radius-md) var(--border-radius-md) 0;
  transition: border-color var(--transition-speed) var(--transition-timing),
              box-shadow var(--transition-speed) var(--transition-timing);
}

.modern-form-control::placeholder {
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Error States */
.modern-form-control.ng-invalid.ng-touched {
  border-color: var(--danger);
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.modern-form-control.is-invalid {
  border-color: var(--danger);
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Titreşim animasyonu için sınıf */
.shake-animation {
  animation: shake 0.6s cubic-bezier(.36,.07,.19,.97) both;
  transform: translate3d(0, 0, 0);
  backface-visibility: hidden;
  perspective: 1000px;
  border-color: #dc3545 !important;
  box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.5) !important;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-6px); }
  20%, 40%, 60%, 80% { transform: translateX(6px); }
}

.modern-form-control:focus {
  border-color: var(--primary);
  outline: 0;
  box-shadow: 0 0 0 0.2rem var(--primary-light);
}

.col-auto {
  width: 1%;
  min-width: 0;
}

/* Button Styles */
.modern-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-weight: 500;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  gap: 0.5rem;
}

.modern-btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.modern-btn-primary:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
}

.modern-btn-primary:disabled {
  background-color: #a0aec0;
  cursor: not-allowed;
  transform: none;
}

.modern-btn-success {
  background-color: #28a745;
  color: white;
}

.modern-btn-success:hover {
  background-color: #218838;
  transform: translateY(-2px);
}

.modern-btn-warning {
  background-color: #ffc107;
  color: #212529;
}

.modern-btn-warning:hover {
  background-color: #e0a800;
  transform: translateY(-2px);
}

.modern-btn-outline-primary {
  background-color: transparent;
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.modern-btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-2px);
}

/* Progress Bar */
.progress {
  display: flex;
  height: 6px;
  overflow: hidden;
  font-size: 0.75rem;
  background-color: #e9ecef;
  border-radius: 0.25rem;
}

.progress-bar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  overflow: hidden;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: var(--primary-color);
  transition: width 0.6s ease;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Dark Mode Support */
[data-theme="dark"] .content-blur {
  filter: blur(3px) brightness(0.7);
}

[data-theme="dark"] .form-section {
  background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .form-section:hover {
  background-color: rgba(255, 255, 255, 0.08);
}

[data-theme="dark"] .input-group-text {
  background-color: var(--primary-light);
  border-color: var(--border-color);
  color: var(--primary);
}

[data-theme="dark"] .input-group-text.text-danger {
  background-color: var(--danger-light);
  color: var(--danger);
}

[data-theme="dark"] .progress {
  background-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .modern-btn-primary:disabled {
  background-color: #4a5568;
}

/* Registration Toggle Button Styles */
.registration-toggle-btn {
  background-color: var(--bg-secondary, #f8f9fa);
  border: 1px solid var(--border-color, #dee2e6);
  color: var(--text-primary, #495057);
  border-radius: 8px;
  font-size: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  min-width: 44px;
  min-height: 44px;
}

.registration-toggle-btn:hover {
  background-color: var(--bg-tertiary, #e9ecef);
  border-color: var(--primary, #007bff);
  color: var(--primary, #007bff);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.registration-toggle-btn:focus {
  outline: none;
  border-color: var(--primary, #007bff);
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.registration-toggle-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.registration-toggle-btn i {
  font-size: 14px;
  transition: transform 0.2s ease;
}

.registration-toggle-btn:hover i {
  transform: scale(1.1);
}

/* Dark Mode Support for Registration Toggle */
[data-theme="dark"] .registration-toggle-btn {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .registration-toggle-btn:hover {
  background-color: rgba(255, 255, 255, 0.15);
  border-color: var(--primary, #0d6efd);
  color: var(--primary, #0d6efd);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

[data-theme="dark"] .registration-toggle-btn:focus {
  border-color: var(--primary, #0d6efd);
  box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.25);
}

/* Dropdown Menu Dark Mode - Improved */
[data-theme="dark"] .dropdown-menu {
  background-color: #1a1d23 !important;
  border: 1px solid #3a3f47 !important;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.6) !important;
}

[data-theme="dark"] .dropdown-item {
  color: #e2e8f0 !important;
  background-color: transparent !important;
}

[data-theme="dark"] .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:focus {
  background-color: #2d3748 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .dropdown-item.active {
  background-color: #4299e1 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .dropdown-header {
  color: #a0aec0 !important;
  background-color: transparent !important;
}

[data-theme="dark"] .dropdown-divider {
  border-color: #3a3f47 !important;
  margin: 0.5rem 0 !important;
}

/* Badge colors in dark mode */
[data-theme="dark"] .badge.bg-primary {
  background-color: #4299e1 !important;
  color: #ffffff !important;
}

[data-theme="dark"] .badge.bg-success {
  background-color: #48bb78 !important;
  color: #ffffff !important;
}

/* Responsive Adjustments */
@media (max-width: 767.98px) {
  .card-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .card-header button {
    margin-top: 1rem;
    width: 100%;
  }

  .card-footer {
    flex-direction: column;
    gap: 1rem;
  }

  .card-footer button,
  .card-footer a {
    width: 100%;
  }

  .registration-toggle-btn {
    min-width: 40px;
    min-height: 40px;
    font-size: 14px;
  }

  .registration-toggle-btn i {
    font-size: 12px;
  }
}
